import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import ResourceLinkDialog from './ResourceLinkDialog'
import ResourceViewer from './ResourceViewer'
import { databaseApi, fileSystemApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { useLanguage } from '../../contexts/LanguageContext'
import { cn } from '../../lib/utils'
import type { ResourceLink } from '../../../../shared/types'

interface UnifiedResourcesProps {
  projectId?: string
  areaId?: string
  className?: string
  compact?: boolean // 是否使用紧凑模式
}

export function UnifiedResources({ projectId, areaId, className, compact = false }: UnifiedResourcesProps) {
  const [resources, setResources] = useState<ResourceLink[]>([])
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUIStore()
  const { confirm: showConfirmDialog, ConfirmDialog } = useConfirmDialog()
  const { t } = useLanguage()

  // Load resources on component mount
  useEffect(() => {
    loadResources()
  }, [projectId, areaId])

  const loadResources = async () => {
    if (!projectId && !areaId) return

    try {
      setLoading(true)
      const result = projectId
        ? await databaseApi.getProjectResources(projectId)
        : await databaseApi.getAreaResources(areaId!)

      if (result.success) {
        setResources(result.data || [])
      } else {
        console.error('Failed to load resources:', result.error)
        addNotification({
          type: 'error',
          title: 'Failed to load resources',
          message: result.error || 'Unknown error occurred'
        })
      }
    } catch (error) {
      console.error('Error loading resources:', error)
      addNotification({
        type: 'error',
        title: 'Error loading resources',
        message: 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleResourcesLinked = (newResources: ResourceLink[]) => {
    setResources(prev => [...prev, ...newResources])
  }

  const handleUnlinkResource = async (resource: ResourceLink) => {
    const entityType = projectId ? 'project' : 'area'
    const translationKey = projectId ? 'pages.projects.detail.resources' : 'pages.areas.detail.resources'

    const confirmed = await showConfirmDialog({
      title: t(`${translationKey}.confirmDialogs.unlinkResource.title`),
      message: t(`${translationKey}.confirmDialogs.unlinkResource.message`, {
        name: getResourceDisplayName(resource),
        type: t(`common.${entityType}`)
      }),
      confirmText: t(`${translationKey}.confirmDialogs.unlinkResource.confirmText`),
      cancelText: t(`${translationKey}.confirmDialogs.unlinkResource.cancelText`),
      variant: 'destructive'
    })

    if (confirmed) {
      try {
        const result = projectId
          ? await databaseApi.unlinkResourceFromProject(resource.id, projectId)
          : await databaseApi.unlinkResourceFromArea(resource.id, areaId!)

        if (result.success) {
          setResources(prev => prev.filter(r => r.id !== resource.id))
          addNotification({
            type: 'success',
            title: t(`${translationKey}.notifications.unlinkSuccess`),
            message: t(`${translationKey}.notifications.unlinkSuccessMessage`, {
              name: getResourceDisplayName(resource),
              type: t(`common.${entityType}`)
            })
          })
        } else {
          throw new Error(result.error || t(`${translationKey}.notifications.unlinkError`))
        }
      } catch (error) {
        console.error('Failed to unlink resource:', error)
        addNotification({
          type: 'error',
          title: t(`${translationKey}.notifications.unlinkError`),
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        })
      }
    }
  }

  const [selectedResource, setSelectedResource] = useState<ResourceLink | null>(null)
  const [isResourceViewerOpen, setIsResourceViewerOpen] = useState(false)

  const handleOpenResource = async (resource: ResourceLink) => {
    try {
      // Check if file exists
      const existsResult = await fileSystemApi.fileExists(resource.resourcePath)

      if (!existsResult.success || !existsResult.data) {
        addNotification({
          type: 'error',
          title: t('pages.projects.detail.resources.notifications.fileNotFound'),
          message: t('pages.projects.detail.resources.notifications.fileNotFoundMessage', { path: resource.resourcePath })
        })
        return
      }

      // Open resource in modal viewer
      setSelectedResource(resource)
      setIsResourceViewerOpen(true)

    } catch (error) {
      console.error('Failed to open resource:', error)
      addNotification({
        type: 'error',
        title: 'Failed to open resource',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const getResourceDisplayName = (resource: ResourceLink) => {
    if (resource.title) return resource.title
    
    // Extract filename from path
    const pathParts = resource.resourcePath.split(/[/\\]/)
    const filename = pathParts[pathParts.length - 1]
    return filename.replace('.md', '')
  }

  const getResourcePath = (resource: ResourceLink) => {
    // Show relative path for better readability
    const pathParts = resource.resourcePath.split(/[/\\]/)
    if (pathParts.length > 3) {
      return `.../${pathParts.slice(-3).join('/')}`
    }
    return resource.resourcePath
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Linked Resources</CardTitle>
          <CardDescription>Loading resources...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className={`flex items-center gap-2 ${compact ? 'text-lg' : 'text-xl'}`}>
                {compact ? (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    关联资源
                  </>
                ) : (
                  t(projectId ? 'pages.projects.detail.resources.title' : 'pages.areas.detail.resources.title')
                )}
                {resources.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {resources.length}
                  </Badge>
                )}
              </CardTitle>
              {!compact && (
                <CardDescription>
                  {t(projectId ? 'pages.projects.detail.resources.description' : 'pages.areas.detail.resources.description')}
                </CardDescription>
              )}
            </div>
            <Button variant={compact ? "outline" : "default"} size="sm" onClick={() => setIsLinkDialogOpen(true)}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
              {compact ? '链接资源' : t(projectId ? 'pages.projects.detail.resources.linkResource' : 'pages.areas.detail.resources.linkResource')}
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {resources.length === 0 ? (
            <div className={`text-center ${compact ? 'py-6' : 'py-8'} text-muted-foreground`}>
              {compact ? (
                <svg className="h-12 w-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              ) : (
                <div className="text-4xl mb-2">🔗</div>
              )}
              <p className="text-sm">
                {compact ? '暂无关联资源' : t(projectId ? 'pages.projects.detail.resources.noResources' : 'pages.areas.detail.resources.noResources')}
              </p>
              <p className="text-xs mt-1">
                {compact ? '点击"链接资源"添加相关文档' : t(projectId ? 'pages.projects.detail.resources.noResourcesHint' : 'pages.areas.detail.resources.noResourcesHint')}
              </p>
            </div>
          ) : (
            <div className={compact ? "space-y-2" : "space-y-3"}>
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className={`group flex items-center justify-between ${compact ? 'p-2' : 'p-3'} border rounded-lg hover:bg-accent/50 transition-colors`}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">
                          {getResourceDisplayName(resource)}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          .md
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground truncate">
                        {getResourcePath(resource)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenResource(resource)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 5v.01M12 12v.01M12 19v.01"
                            />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleOpenResource(resource)}>
                          {t(projectId ? 'pages.projects.detail.resources.actions.open' : 'pages.areas.detail.resources.actions.open')}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleUnlinkResource(resource)}
                          className="text-red-600 focus:text-red-600"
                        >
                          {t(projectId ? 'pages.projects.detail.resources.actions.unlink' : 'pages.areas.detail.resources.actions.unlink')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resource Link Dialog */}
      <ResourceLinkDialog
        isOpen={isLinkDialogOpen}
        onClose={() => setIsLinkDialogOpen(false)}
        projectId={projectId}
        areaId={areaId}
        onResourcesLinked={handleResourcesLinked}
      />

      {/* Resource Viewer */}
      <ResourceViewer
        resource={selectedResource}
        isOpen={isResourceViewerOpen}
        onClose={() => {
          setIsResourceViewerOpen(false)
          setSelectedResource(null)
        }}
      />

      {/* Confirm Dialog */}
      <ConfirmDialog />
    </>
  )
}

// 向后兼容的导出
export const ProjectResources = UnifiedResources

export default ProjectResources
